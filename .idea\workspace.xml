<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="BackendCodeEditorMiscSettings">
    <option name="/Default/Environment/Hierarchy/GeneratedFilesCacheKey/Timestamp/@EntryValue" value="4" type="long" />
    <option name="/Default/Environment/StickyLines/Enabled/@EntryValue" value="false" type="bool" />
    <option name="/Default/Housekeeping/FeatureSuggestion/FeatureSuggestionManager/DisabledSuggesters/=SwitchToGoToActionSuggester/@EntryIndexedValue" value="true" type="bool" />
    <option name="/Default/Housekeeping/GlobalSettingsUpgraded/IsUpgraded/@EntryValue" value="true" type="bool" />
    <option name="/Default/RiderDebugger/RiderRestoreDecompile/RestoreDecompileSetting/@EntryValue" value="false" type="bool" />
  </component>
  <component name="CMakePresetLoader">{
  &quot;useNewFormat&quot;: true
}</component>
  <component name="CMakeProjectFlavorService">
    <option name="flavorId" value="CMakePlainProjectFlavor" />
  </component>
  <component name="CMakeReloadState">
    <option name="reloaded" value="true" />
  </component>
  <component name="CMakeRunConfigurationManager">
    <generated>
      <config projectName="aistreamer" targetName="gstaivideoencoder" />
      <config projectName="aistreamer" targetName="gstaimultistream" />
      <config projectName="aistreamer" targetName="testmeta" />
      <config projectName="aistreamer" targetName="gstaiinfer" />
      <config projectName="aistreamer" targetName="gstaitracker" />
      <config projectName="aistreamer" targetName="test_aimeta_api" />
      <config projectName="aistreamer" targetName="aimeta" />
      <config projectName="aistreamer" targetName="testmemorypool" />
      <config projectName="aistreamer" targetName="testdecode" />
      <config projectName="aistreamer" targetName="testosd" />
      <config projectName="aistreamer" targetName="iva-pipeline-multistream" />
      <config projectName="aistreamer" targetName="aiosd" />
      <config projectName="aistreamer" targetName="gstivaplugin1" />
      <config projectName="aistreamer" targetName="iva-app" />
      <config projectName="aistreamer" targetName="gstaivideodecoder" />
      <config projectName="aistreamer" targetName="gstaivideoconverter" />
      <config projectName="aistreamer" targetName="aicommon" />
      <config projectName="aistreamer" targetName="teststreammux" />
      <config projectName="aistreamer" targetName="aiinfer" />
      <config projectName="aistreamer" targetName="testinfer" />
      <config projectName="aistreamer" targetName="aibufsurface" />
      <config projectName="aistreamer" targetName="gst_metatest" />
      <config projectName="aistreamer" targetName="aibufsurftransform" />
      <config projectName="aistreamer" targetName="testencode" />
      <config projectName="aistreamer" targetName="yolov8infer_parser" />
    </generated>
  </component>
  <component name="CMakeSettings">
    <configurations>
      <configuration PROFILE_NAME="Debug" ENABLED="true" CONFIG_NAME="Debug" />
    </configurations>
  </component>
  <component name="ChangeListManager">
    <list default="true" id="87ac54f5-a3e8-4ce5-a67d-a54bf8ed437d" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/iva/.gitmodules" beforeDir="false" afterPath="$PROJECT_DIR$/iva/.gitmodules" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/iva/iva-app/plugins/gst-image-analyser/gst_image_analyser.cpp" beforeDir="false" afterPath="$PROJECT_DIR$/iva/iva-app/plugins/gst-image-analyser/gst_image_analyser.cpp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/iva/iva-app/src/pipeline_core.cpp" beforeDir="false" afterPath="$PROJECT_DIR$/iva/iva-app/src/pipeline_core.cpp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/iva/iva-app/src/pipeline_manager.cpp" beforeDir="false" afterPath="$PROJECT_DIR$/iva/iva-app/src/pipeline_manager.cpp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/iva/platforms/npu" beforeDir="false" afterPath="$PROJECT_DIR$/iva/platforms/npu" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/iva/platforms/npu/samples/iva-pipeline-multistream/main.cpp" beforeDir="false" afterPath="$PROJECT_DIR$/iva/platforms/npu/samples/iva-pipeline-multistream/main.cpp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/iva/platforms/npu/samples/iva-pipeline-multistream/pipeline_core.cpp" beforeDir="false" afterPath="$PROJECT_DIR$/iva/platforms/npu/samples/iva-pipeline-multistream/pipeline_core.cpp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/iva/platforms/npu/samples/iva-pipeline-multistream/pipeline_sink.cpp" beforeDir="false" afterPath="$PROJECT_DIR$/iva/platforms/npu/samples/iva-pipeline-multistream/pipeline_sink.cpp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/iva/platforms/npu/samples/iva-pipeline-multistream/pipeline_source.cpp" beforeDir="false" afterPath="$PROJECT_DIR$/iva/platforms/npu/samples/iva-pipeline-multistream/pipeline_source.cpp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/iva/platforms/npu/sources/decode/gst_aibuffer_pool.cpp" beforeDir="false" afterPath="$PROJECT_DIR$/iva/platforms/npu/sources/decode/gst_aibuffer_pool.cpp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/iva/platforms/npu/sources/decode/gst_aideviceframe_allocator.cpp" beforeDir="false" afterPath="$PROJECT_DIR$/iva/platforms/npu/sources/decode/gst_aideviceframe_allocator.cpp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/iva/platforms/npu/sources/decode/gst_aivideodecoder.cpp" beforeDir="false" afterPath="$PROJECT_DIR$/iva/platforms/npu/sources/decode/gst_aivideodecoder.cpp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/iva/platforms/npu/sources/decode/videoDecoder/huawei/v2/310pai_video_decoder.cpp" beforeDir="false" afterPath="$PROJECT_DIR$/iva/platforms/npu/sources/decode/videoDecoder/huawei/v2/310pai_video_decoder.cpp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/iva/platforms/npu/sources/decode/videoDecoder/huawei/v2/310pai_video_decoder.h" beforeDir="false" afterPath="$PROJECT_DIR$/iva/platforms/npu/sources/decode/videoDecoder/huawei/v2/310pai_video_decoder.h" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/iva/platforms/npu/sources/decode/videoDecoder/huawei/v2/vcodec_channel_manage.cpp" beforeDir="false" afterPath="$PROJECT_DIR$/iva/platforms/npu/sources/decode/videoDecoder/huawei/v2/vcodec_channel_manage.cpp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/iva/platforms/npu/sources/decode/videoDecoder/huawei/v2/vcodec_channel_manage.h" beforeDir="false" afterPath="$PROJECT_DIR$/iva/platforms/npu/sources/decode/videoDecoder/huawei/v2/vcodec_channel_manage.h" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/iva/platforms/npu/sources/decode/videoDecoder/huawei/v2/video_decoder_context.h" beforeDir="false" afterPath="$PROJECT_DIR$/iva/platforms/npu/sources/decode/videoDecoder/huawei/v2/video_decoder_context.h" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/iva/platforms/npu/sources/decode/videoDecoder/video_data_pool.cpp" beforeDir="false" afterPath="$PROJECT_DIR$/iva/platforms/npu/sources/decode/videoDecoder/video_data_pool.cpp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/iva/platforms/npu/sources/decode/videoDecoder/video_data_pool.h" beforeDir="false" afterPath="$PROJECT_DIR$/iva/platforms/npu/sources/decode/videoDecoder/video_data_pool.h" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/iva/platforms/npu/sources/decode/videoDecoder/video_decoder.cpp" beforeDir="false" afterPath="$PROJECT_DIR$/iva/platforms/npu/sources/decode/videoDecoder/video_decoder.cpp" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="CMakeBuildProfile:Debug" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/iva/platforms/npu" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///dummy.cpp" root0="SKIP_HIGHLIGHTING" />
  </component>
  <component name="ProjectApplicationVersion">
    <option name="ide" value="CLion" />
    <option name="majorVersion" value="2025" />
    <option name="minorVersion" value="2" />
    <option name="productBranch" value="Classic" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="30zBUdQhubI6gLpF1BLbyXP2dkJ" />
  <component name="ProjectStatusUIService">{
  &quot;uiKindId&quot;: &quot;Widget&quot;
}</component>
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "CMake 应用程序.iva-app.executor": "Run",
    "CMake 应用程序.iva-pipeline-multistream.executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.RadMigrateCodeStyle": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager.252": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "RunOnceActivity.west.config.association.type.startup.service": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "last_opened_file_path": "E:/Project/IVA/iva/Release_Huawei",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "settings.sync",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected="CMake 应用程序.iva-pipeline-multistream">
    <configuration default="true" type="CLionExternalRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true">
      <method v="2">
        <option name="CLION.EXTERNAL.BUILD" enabled="true" />
      </method>
    </configuration>
    <configuration name="aibufsurface" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="aistreamer" TARGET_NAME="aibufsurface" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="aibufsurftransform" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="aistreamer" TARGET_NAME="aibufsurftransform" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="aicommon" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="aistreamer" TARGET_NAME="aicommon" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="aiinfer" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="aistreamer" TARGET_NAME="aiinfer" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="aimeta" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="aistreamer" TARGET_NAME="aimeta" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="aiosd" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="aistreamer" TARGET_NAME="aiosd" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="gst_metatest" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="aistreamer" TARGET_NAME="gst_metatest" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="gstaiinfer" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="aistreamer" TARGET_NAME="gstaiinfer" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="gstaimultistream" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="aistreamer" TARGET_NAME="gstaimultistream" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="gstaitracker" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="aistreamer" TARGET_NAME="gstaitracker" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="gstaivideoconverter" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="aistreamer" TARGET_NAME="gstaivideoconverter" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="gstaivideodecoder" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="aistreamer" TARGET_NAME="gstaivideodecoder" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="gstaivideoencoder" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="aistreamer" TARGET_NAME="gstaivideoencoder" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="gstivaplugin1" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="aistreamer" TARGET_NAME="gstivaplugin1" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="iva-app" type="CMakeRunConfiguration" factoryName="Application" PROGRAM_PARAMS="16 1" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="aistreamer" RUN_TARGET_NAME="iva-app" EXPLICIT_BUILD_TARGET_NAME="all">
      <envs>
        <env name="ASCEND_GLOBAL_LOG_LEVEL" value="3" />
        <env name="ASCEND_SLOG_PRINT_TO_STDOUT" value="1" />
        <env name="GST_DEBUG" value="ERROR,aivideodecoder:INFO" />
        <env name="GST_PLUGIN_PATH" value="/usr/lib/x86_64-linux-gnu/gstreamer-1.0:./plugins" />
        <env name="LD_LIBRARY_PATH" value="/usr/local/Ascend/ascend-toolkit/latest/acllib/lib64:./plugins:/home/<USER>/opencv/lib:$LD_LIBRARY_PATH" />
      </envs>
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="iva-pipeline-multistream" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="aistreamer" RUN_TARGET_NAME="iva-pipeline-multistream" EXPLICIT_BUILD_TARGET_NAME="all">
      <envs>
        <env name="ASCEND_GLOBAL_LOG_LEVEL" value="3" />
        <env name="ASCEND_SLOG_PRINT_TO_STDOUT" value="1" />
        <env name="GST_DEBUG" value="ERROR,aivideodecoder:INFO" />
        <env name="GST_PLUGIN_PATH" value="/usr/lib/x86_64-linux-gnu/gstreamer-1.0:./plugins" />
        <env name="LD_LIBRARY_PATH" value="/usr/local/Ascend/ascend-toolkit/latest/acllib/lib64:./plugins:/home/<USER>/opencv/lib:$LD_LIBRARY_PATH" />
      </envs>
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="test_aimeta_api" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="aistreamer" TARGET_NAME="test_aimeta_api" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="aistreamer" RUN_TARGET_NAME="test_aimeta_api">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="testdecode" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="aistreamer" TARGET_NAME="testdecode" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="aistreamer" RUN_TARGET_NAME="testdecode">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="testencode" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="aistreamer" TARGET_NAME="testencode" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="aistreamer" RUN_TARGET_NAME="testencode">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="testinfer" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="aistreamer" TARGET_NAME="testinfer" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="aistreamer" RUN_TARGET_NAME="testinfer">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="testmemorypool" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="aistreamer" TARGET_NAME="testmemorypool" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="aistreamer" RUN_TARGET_NAME="testmemorypool">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="testmeta" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="aistreamer" TARGET_NAME="testmeta" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="aistreamer" RUN_TARGET_NAME="testmeta">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="testosd" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="aistreamer" TARGET_NAME="testosd" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="aistreamer" RUN_TARGET_NAME="testosd">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="teststreammux" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="aistreamer" TARGET_NAME="teststreammux" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="aistreamer" RUN_TARGET_NAME="teststreammux">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="yolov8infer_parser" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="aistreamer" TARGET_NAME="yolov8infer_parser" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="CMake 应用程序.iva-app" />
      <item itemvalue="CMake 应用程序.iva-pipeline-multistream" />
      <item itemvalue="CMake 应用程序.gstivaplugin1" />
      <item itemvalue="CMake 应用程序.gstaivideoconverter" />
      <item itemvalue="CMake 应用程序.gstaivideodecoder" />
      <item itemvalue="CMake 应用程序.gstaivideoencoder" />
      <item itemvalue="CMake 应用程序.gstaiinfer" />
      <item itemvalue="CMake 应用程序.aiinfer" />
      <item itemvalue="CMake 应用程序.yolov8infer_parser" />
      <item itemvalue="CMake 应用程序.gstaimultistream" />
      <item itemvalue="CMake 应用程序.aiosd" />
      <item itemvalue="CMake 应用程序.aimeta" />
      <item itemvalue="CMake 应用程序.gstaitracker" />
      <item itemvalue="CMake 应用程序.aibufsurface" />
      <item itemvalue="CMake 应用程序.aibufsurftransform" />
      <item itemvalue="CMake 应用程序.aicommon" />
      <item itemvalue="CMake 应用程序.test_aimeta_api" />
      <item itemvalue="CMake 应用程序.testdecode" />
      <item itemvalue="CMake 应用程序.testencode" />
      <item itemvalue="CMake 应用程序.testinfer" />
      <item itemvalue="CMake 应用程序.testmemorypool" />
      <item itemvalue="CMake 应用程序.testmeta" />
      <item itemvalue="CMake 应用程序.gst_metatest" />
      <item itemvalue="CMake 应用程序.testosd" />
      <item itemvalue="CMake 应用程序.teststreammux" />
    </list>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="87ac54f5-a3e8-4ce5-a67d-a54bf8ed437d" name="更改" comment="" />
      <created>1754620538735</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754620538735</updated>
      <workItem from="1754620540471" duration="26251000" />
      <workItem from="1755074138516" duration="71286000" />
      <workItem from="1755587975355" duration="32529000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VCPKGProject">
    <isAutomaticCheckingOnLaunch value="false" />
    <isAutomaticFoundErrors value="true" />
    <isAutomaticReloadCMake value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/iva/platforms/npu/sources/decode/common/video_data_pool.cpp</url>
          <line>6</line>
          <properties>
            <option name="hitCountValue" value="1" />
          </properties>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/iva/platforms/npu/sources/infer/aiinfer/huawei/model_process.cpp</url>
          <line>171</line>
          <properties>
            <option name="hitCountValue" value="1" />
          </properties>
          <option name="timeStamp" value="16" />
        </line-breakpoint>
        <line-breakpoint type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/iva/platforms/npu/sources/infer/aiinfer/huawei/model_process.cpp</url>
          <line>307</line>
          <properties>
            <option name="hitCountValue" value="1" />
          </properties>
          <option name="timeStamp" value="17" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/iva/platforms/npu/sources/decode/gst_aivideodecoder.cpp</url>
          <line>248</line>
          <option name="timeStamp" value="39" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/iva/platforms/npu/sources/decode/gst_aivideodecoder.cpp</url>
          <line>340</line>
          <option name="timeStamp" value="40" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/iva/platforms/npu/sources/decode/gst_aivideodecoder.cpp</url>
          <line>527</line>
          <option name="timeStamp" value="41" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/iva/platforms/npu/sources/decode/gst_aivideodecoder.cpp</url>
          <line>142</line>
          <option name="timeStamp" value="42" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/iva/platforms/npu/sources/decode/gst_aivideodecoder.cpp</url>
          <line>163</line>
          <option name="timeStamp" value="44" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>